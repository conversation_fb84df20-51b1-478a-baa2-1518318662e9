import type { SkillAnalysis, SkillMatchAnalysis } from './types';

/**
 * Reduces multiple single-resume skill analyses into a consolidated result.
 *
 * This function takes the results from analyzing multiple resumes individually
 * and combines them into the format expected by the existing multi-resume API,
 * maintaining backward compatibility while using the new single-resume architecture.
 *
 * @param analyses - Array of single-resume skill analysis results
 * @returns Consolidated skill match analysis in the original format
 *
 * @example
 * ```typescript
 * const results = await Promise.all(
 *   resumes.map(resume => analyzeSkills(resume, job, model))
 * );
 *
 * const consolidatedResult = reduceSkillAnalyses(results);
 * console.log(`Total skills: ${consolidatedResult.consolidatedSkills.length}`);
 * console.log(`Coverage: ${consolidatedResult.summary.coveragePercentage}%`);
 * ```
 */
export function reduceSkillAnalyses(
  analyses: SkillAnalysis[]
): SkillMatchAnalysis {
  if (analyses.length === 0) {
    return createEmptySkillAnalysis();
  }

  // Consolidate skills from all resumes
  const consolidatedSkills = consolidateSkills(analyses);

  // Consolidate direct matches
  const directMatches = consolidateDirectMatches(analyses);

  // Consolidate transferable skills
  const transferableSkills = consolidateTransferableSkills(analyses);

  // Consolidate missing skills (intersection of all missing skills)
  const missingSkills = consolidateMissingSkills(analyses);

  // Calculate consolidated summary
  const summary = calculateConsolidatedSummary(
    consolidatedSkills,
    directMatches,
    transferableSkills,
    missingSkills,
    analyses
  );

  return {
    consolidatedSkills,
    directMatches,
    transferableSkills,
    missingSkills,
    summary,
  };
}

/**
 * Creates an empty skill analysis result
 */
function createEmptySkillAnalysis(): SkillMatchAnalysis {
  return {
    consolidatedSkills: [],
    directMatches: [],
    transferableSkills: [],
    missingSkills: [],
    summary: {
      totalJobSkills: 0,
      totalResumeSkills: 0,
      directMatchCount: 0,
      transferableMatchCount: 0,
      missingSkillCount: 0,
      coveragePercentage: 0,
    },
  };
}

/**
 * Consolidates skills from multiple resumes, removing duplicates and merging sources
 */
function consolidateSkills(
  analyses: SkillAnalysis[]
): SkillMatchAnalysis['consolidatedSkills'] {
  const skillMap = new Map<
    string,
    SkillMatchAnalysis['consolidatedSkills'][0]
  >();

  for (const analysis of analyses) {
    for (const skill of analysis.resumeSkills) {
      const normalizedName = skill.name.toLowerCase().trim();

      if (skillMap.has(normalizedName)) {
        // Merge with existing skill
        const existing = skillMap.get(normalizedName)!;
        existing.keywords = Array.from(
          new Set([...existing.keywords, ...skill.keywords])
        );
        existing.sources = Array.from(
          new Set([...existing.sources, skill.source])
        );

        // Keep the most detailed source section info
        if (skill.sourceSection && !existing.sourceSection) {
          existing.sourceSection = skill.sourceSection;
        }
      } else {
        // Add new skill
        skillMap.set(normalizedName, {
          name: skill.name,
          keywords: [...skill.keywords],
          sources: [skill.source],
          sourceSection: skill.sourceSection,
        });
      }
    }
  }

  return Array.from(skillMap.values()).sort((a, b) =>
    a.name.localeCompare(b.name)
  );
}

/**
 * Consolidates direct matches from multiple resumes
 */
function consolidateDirectMatches(
  analyses: SkillAnalysis[]
): SkillMatchAnalysis['directMatches'] {
  const matchMap = new Map<string, SkillMatchAnalysis['directMatches'][0]>();

  for (const analysis of analyses) {
    for (const match of analysis.directMatches) {
      const key = `${match.jobSkill.toLowerCase()}-${match.resumeSkill.toLowerCase()}`;

      if (matchMap.has(key)) {
        // Merge sources for existing match
        const existing = matchMap.get(key)!;
        existing.sources = Array.from(
          new Set([...existing.sources, match.source])
        );
      } else {
        // Add new match
        matchMap.set(key, {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          matchType: match.matchType,
          sources: [match.source],
          sourceSection: match.sourceSection,
        });
      }
    }
  }

  return Array.from(matchMap.values()).sort((a, b) => {
    // Sort by match type priority: exact > synonym > keyword
    const typePriority = { exact: 3, synonym: 2, keyword: 1 };
    const priorityDiff = typePriority[b.matchType] - typePriority[a.matchType];
    if (priorityDiff !== 0) return priorityDiff;

    return a.jobSkill.localeCompare(b.jobSkill);
  });
}

/**
 * Consolidates transferable skills from multiple resumes
 */
function consolidateTransferableSkills(
  analyses: SkillAnalysis[]
): SkillMatchAnalysis['transferableSkills'] {
  const transferableMap = new Map<
    string,
    SkillMatchAnalysis['transferableSkills'][0]
  >();

  for (const analysis of analyses) {
    for (const skill of analysis.transferableSkills) {
      const key = `${skill.jobSkill.toLowerCase()}-${skill.resumeSkill.toLowerCase()}`;

      if (transferableMap.has(key)) {
        // Keep the match with higher confidence
        const existing = transferableMap.get(key)!;
        if (skill.confidenceRating > existing.confidenceRating) {
          existing.confidenceRating = skill.confidenceRating;
          existing.reasoning = skill.reasoning;
        }
        existing.sources = Array.from(
          new Set([...existing.sources, skill.source])
        );
      } else {
        // Add new transferable skill
        transferableMap.set(key, {
          jobSkill: skill.jobSkill,
          resumeSkill: skill.resumeSkill,
          confidenceRating: skill.confidenceRating,
          reasoning: skill.reasoning,
          sources: [skill.source],
          sourceSection: skill.sourceSection,
        });
      }
    }
  }

  return Array.from(transferableMap.values()).sort(
    (a, b) => b.confidenceRating - a.confidenceRating
  );
}

/**
 * Consolidates missing skills (skills that are missing across ALL resumes)
 */
function consolidateMissingSkills(
  analyses: SkillAnalysis[]
): SkillMatchAnalysis['missingSkills'] {
  if (analyses.length === 0) {
    return [];
  }

  // Start with missing skills from the first analysis
  let commonMissingSkills = new Set(
    analyses[0].missingSkills.map((skill) => skill.name.toLowerCase())
  );

  // Find intersection with missing skills from other analyses
  for (let i = 1; i < analyses.length; i++) {
    const currentMissingSkills = new Set(
      analyses[i].missingSkills.map((skill) => skill.name.toLowerCase())
    );

    commonMissingSkills = new Set(
      [...commonMissingSkills].filter((skill) =>
        currentMissingSkills.has(skill)
      )
    );
  }

  // Convert back to the original format using the first analysis as reference
  const referenceSkills = analyses[0].missingSkills;
  return referenceSkills
    .filter((skill) => commonMissingSkills.has(skill.name.toLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Calculates consolidated summary statistics
 */
function calculateConsolidatedSummary(
  consolidatedSkills: SkillMatchAnalysis['consolidatedSkills'],
  directMatches: SkillMatchAnalysis['directMatches'],
  transferableSkills: SkillMatchAnalysis['transferableSkills'],
  missingSkills: SkillMatchAnalysis['missingSkills'],
  analyses: SkillAnalysis[]
): SkillMatchAnalysis['summary'] {
  // Use the first analysis to get job skills count (should be same across all)
  const totalJobSkills =
    analyses.length > 0 ? analyses[0].summary.totalJobSkills : 0;

  const totalResumeSkills = consolidatedSkills.length;
  const directMatchCount = directMatches.length;
  const transferableMatchCount = transferableSkills.length;
  const missingSkillCount = missingSkills.length;

  const totalMatches = directMatchCount + transferableMatchCount;
  const coveragePercentage =
    totalJobSkills > 0 ? Math.round((totalMatches / totalJobSkills) * 100) : 0;

  return {
    totalJobSkills,
    totalResumeSkills,
    directMatchCount,
    transferableMatchCount,
    missingSkillCount,
    coveragePercentage,
  };
}

/**
 * Helper function to get skills by source type
 */
export function getSkillsBySource(
  analysis: SkillMatchAnalysis,
  source: 'explicit' | 'work_experience' | 'projects' | 'education'
) {
  return {
    skills: analysis.consolidatedSkills.filter((skill) =>
      skill.sources.includes(source)
    ),
    directMatches: analysis.directMatches.filter((match) =>
      match.sources.includes(source)
    ),
    transferableSkills: analysis.transferableSkills.filter((skill) =>
      skill.sources.includes(source)
    ),
  };
}

/**
 * Helper function to get high-confidence transferable skills
 */
export function getHighConfidenceTransferableSkills(
  analysis: SkillMatchAnalysis,
  minConfidence: 2 | 3 = 2
) {
  return analysis.transferableSkills.filter(
    (skill) => skill.confidenceRating >= minConfidence
  );
}

/**
 * Helper function to get skills by match type
 */
export function getSkillsByMatchType(
  analysis: SkillMatchAnalysis,
  matchType: 'exact' | 'synonym' | 'keyword'
) {
  return analysis.directMatches.filter(
    (match) => match.matchType === matchType
  );
}

/**
 * Helper function to get missing skills by category
 */
export function getMissingSkillsByCategory(
  analysis: SkillMatchAnalysis,
  category?: string
) {
  if (!category) {
    return analysis.missingSkills;
  }
  return analysis.missingSkills.filter((skill) => skill.category === category);
}

/**
 * Helper function to calculate skill coverage by category
 */
export function getSkillCoverageByCategory(analysis: SkillMatchAnalysis) {
  const categories = new Map<string, { total: number; covered: number }>();

  // Count missing skills by category
  for (const skill of analysis.missingSkills) {
    const category = skill.category || 'Other';
    if (!categories.has(category)) {
      categories.set(category, { total: 0, covered: 0 });
    }
    categories.get(category)!.total++;
  }

  // Count covered skills (estimate categories from direct matches)
  for (const match of analysis.directMatches) {
    const category = categorizeSkill(match.jobSkill);
    if (!categories.has(category)) {
      categories.set(category, { total: 0, covered: 0 });
    }
    categories.get(category)!.total++;
    categories.get(category)!.covered++;
  }

  // Convert to percentage coverage
  const result: Record<string, number> = {};
  for (const [category, counts] of categories) {
    result[category] =
      counts.total > 0 ? Math.round((counts.covered / counts.total) * 100) : 0;
  }

  return result;
}

/**
 * Simple skill categorization (could be enhanced)
 */
function categorizeSkill(skillName: string): string {
  const skill = skillName.toLowerCase();

  if (
    skill.includes('javascript') ||
    skill.includes('python') ||
    skill.includes('java')
  ) {
    return 'Programming Language';
  }
  if (
    skill.includes('react') ||
    skill.includes('angular') ||
    skill.includes('vue')
  ) {
    return 'Frontend Framework';
  }
  if (
    skill.includes('node') ||
    skill.includes('express') ||
    skill.includes('django')
  ) {
    return 'Backend Framework';
  }
  if (
    skill.includes('mysql') ||
    skill.includes('postgresql') ||
    skill.includes('mongodb')
  ) {
    return 'Database';
  }
  if (
    skill.includes('aws') ||
    skill.includes('docker') ||
    skill.includes('kubernetes')
  ) {
    return 'Cloud/DevOps';
  }

  return 'Other';
}
