import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { DirectMatcher } from './direct-matcher';
import { SkillExtractor } from './extractor';
import { TransferableSkillExtractor } from './transferable-skills/transferable-skill-extractor';
import type { SkillAnalysis, SkillAnalysisOptions } from './types';
import { SkillMatchError } from './types';

/**
 * Analyzes skill matches between a single resume and a job posting.
 *
 * This function processes exactly one resume against one job description,
 * extracting skills directly from the resume and performing direct/transferable
 * matching against job requirements without any consolidation logic.
 *
 * @param resume - Single resume to analyze
 * @param job - Job posting to match against
 * @param model - LangChain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive single-resume skill analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeSkills(
 *   resume,
 *   jobPosting,
 *   mistralModel,
 *   { includeSourceDetails: true, confidenceThreshold: 2 }
 * );
 *
 * console.log(`Direct matches: ${analysis.directMatches.length}`);
 * console.log(`Coverage: ${analysis.summary.coveragePercentage}%`);
 * ```
 */
export async function analyzeSkills(
  resume: Resume,
  job: Job,
  model: BaseChatModel,
  options: SkillAnalysisOptions = {}
): Promise<SkillAnalysis> {
  try {
    validateInputs(resume, job, model);

    const finalOptions = {
      maxTransferableSkills: 20,
      confidenceThreshold: 1,
      timeoutMs: 30000, // 30 seconds default timeout
      includeSourceDetails: false,
      ...options,
    } satisfies SkillAnalysisOptions;

    // Step 1: Extract skills from the single resume
    const skillExtractor = new SkillExtractor(model);
    const resumeSkills = await skillExtractor.extractSkills(
      resume,
      finalOptions
    );

    // Step 2: Find direct matches (exact, synonym, keyword)
    const directMatcher = new DirectMatcher(model);
    const directMatches = job.skills
      ? await directMatcher.findDirectMatches(
          resumeSkills,
          job.skills,
          finalOptions
        )
      : [];

    // Step 3: Identify unmatched skills for transferable analysis
    const { unmatchedResumeSkills, unmatchedJobSkills } =
      directMatcher.getUnmatchedSkills(
        resumeSkills,
        job.skills ?? [],
        directMatches
      );

    // Step 4: Analyze transferable skills using AI
    const transferableAnalyzer = new TransferableSkillExtractor(model);

    const transferableSkills =
      isNonEmptyArray(unmatchedResumeSkills) &&
      isNonEmptyArray(unmatchedJobSkills)
        ? await transferableAnalyzer.analyzeTransferableSkills(
            unmatchedResumeSkills,
            unmatchedJobSkills,
            finalOptions
          )
        : [];

    // Step 5: Identify missing skills
    const missingSkills = transferableAnalyzer.identifyMissingSkills(
      job.skills,
      directMatches,
      transferableSkills
    );

    // Step 6: Calculate summary statistics
    const summary = calculateSummary(
      resumeSkills,
      job.skills,
      directMatches,
      transferableSkills,
      missingSkills
    );

    return {
      resumeSkills,
      directMatches,
      transferableSkills,
      missingSkills,
      summary,
    };
  } catch (error) {
    throw new SkillMatchError(
      `Failed to analyze single resume skills: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      error
    );
  }
}

/**
 * Validates input parameters
 */
function validateInputs(resume: Resume, job: Job, model: BaseChatModel): void {
  if (!resume) {
    throw new SkillMatchError('Resume is required');
  }

  if (!job) {
    throw new SkillMatchError('Job is required');
  }

  if (!model) {
    throw new SkillMatchError('Model is required');
  }

  if (!job.skills || job.skills.length === 0) {
    console.warn('Job has no skills defined, analysis will be limited');
  }
}

/**
 * Calculates summary statistics for the skill analysis
 */
function calculateSummary(
  resumeSkills: SkillAnalysis['resumeSkills'],
  jobSkills: Job['skills'],
  directMatches: SkillAnalysis['directMatches'],
  transferableSkills: SkillAnalysis['transferableSkills'],
  missingSkills: SkillAnalysis['missingSkills']
): SkillAnalysis['summary'] {
  const totalJobSkills = jobSkills?.length || 0;
  const totalResumeSkills = resumeSkills.length;
  const directMatchCount = directMatches.length;
  const transferableMatchCount = transferableSkills.length;
  const missingSkillCount = missingSkills.length;

  const totalMatches = directMatchCount + transferableMatchCount;
  const coveragePercentage =
    totalJobSkills > 0 ? Math.round((totalMatches / totalJobSkills) * 100) : 0;

  return {
    totalJobSkills,
    totalResumeSkills,
    directMatchCount,
    transferableMatchCount,
    missingSkillCount,
    coveragePercentage,
  };
}

/**
 * Helper function to get skills by source type
 */
export function getSkillsBySource(
  analysis: SkillAnalysis,
  source: 'explicit' | 'work_experience' | 'projects' | 'education'
) {
  return {
    skills: analysis.resumeSkills.filter((skill) => skill.source === source),
    directMatches: analysis.directMatches.filter(
      (match) => match.source === source
    ),
    transferableSkills: analysis.transferableSkills.filter(
      (match) => match.source === source
    ),
  };
}

/**
 * Helper function to get high-confidence transferable skills
 */
export function getHighConfidenceTransferableSkills(
  analysis: SkillAnalysis,
  minConfidence: 2 | 3 = 2
) {
  return analysis.transferableSkills.filter(
    (skill) => skill.confidenceRating >= minConfidence
  );
}

/**
 * Helper function to get skills by match type
 */
export function getSkillsByMatchType(
  analysis: SkillAnalysis,
  matchType: 'exact' | 'synonym' | 'keyword'
) {
  return analysis.directMatches.filter(
    (match) => match.matchType === matchType
  );
}

/**
 * Helper function to get missing skills by category
 */
export function getMissingSkillsByCategory(
  analysis: SkillAnalysis,
  category?: string
) {
  if (!category) {
    return analysis.missingSkills;
  }
  return analysis.missingSkills.filter((skill) => skill.category === category);
}
