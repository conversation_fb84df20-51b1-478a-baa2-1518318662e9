import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { analyzeExperience } from './experience/analyzer';
import { analyzeSkills } from './skills/analyzer';
import { analyzeTitle } from './title/analyzer';
import {
  type AnalysisOptions,
  type AnalysisResult,
  calculateMatchQuality,
  shouldRecommendResume,
} from './types';

/**
 * Orchestrates complete analysis of a single resume against a job posting.
 *
 * This function performs comprehensive analysis including:
 * - Experience matching and scoring
 * - Skills analysis (direct, transferable, missing)
 * - Title matching and optimization
 * - Overall recommendation and summary
 *
 * @param resume - Single resume to analyze
 * @param job - Job posting to match against
 * @param model - LangChain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for all analysis types
 * @returns Promise resolving to complete resume analysis result
 *
 * @example
 * ```typescript
 * const analysis = await analyzeResume(
 *   resume,
 *   jobPosting,
 *   mistralModel,
 *   {
 *     includeSourceDetails: true,
 *     experience: { includeImprovements: true },
 *     skills: { confidenceThreshold: 2 },
 *     title: { suggestImprovements: true }
 *   }
 * );
 *
 * console.log(`Overall score: ${analysis.overallSummary.totalScore}`);
 * console.log(`Match quality: ${analysis.overallSummary.matchQuality}`);
 * console.log(`Recommended: ${analysis.overallSummary.isRecommended}`);
 * ```
 */
export async function analyzeResume(
  resume: Resume,
  job: Job,
  model: BaseChatModel,
  options: AnalysisOptions = {}
): Promise<AnalysisResult> {
  try {
    validateInputs(resume, job, model);

    const finalOptions = {
      includeSourceDetails: false,
      timeoutMs: 60000, // 1 minute total timeout
      ...options,
    };

    // Run all three analyses in parallel for efficiency
    const [experienceAnalysis, skillsAnalysis, titleAnalysis] =
      await Promise.all([
        analyzeExperience(resume, job, model, finalOptions.experience),
        analyzeSkills(resume, job, model, finalOptions.skills),
        analyzeTitle(resume, job, model, finalOptions.title),
      ]);

    // Calculate overall summary
    const overallSummary = calculateOverallSummary(
      experienceAnalysis,
      skillsAnalysis,
      titleAnalysis
    );

    return {
      experience: experienceAnalysis,
      skills: skillsAnalysis,
      title: titleAnalysis,
      overallSummary,
    };
  } catch (error) {
    throw new Error(
      `Failed to analyze single resume: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Analyzes multiple resumes against a single job posting.
 *
 * This function processes multiple resumes individually and returns
 * an array of complete analysis results, maintaining the order of input resumes.
 *
 * @param resumes - Array of resumes to analyze
 * @param job - Job posting to match against
 * @param model - LangChain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for all analysis types
 * @returns Promise resolving to array of resume analysis results
 *
 * @example
 * ```typescript
 * const analyses = await analyzeMultipleResumes(
 *   [resume1, resume2, resume3],
 *   jobPosting,
 *   mistralModel,
 *   { skills: { confidenceThreshold: 2 } }
 * );
 *
 * // Sort by overall score
 * const ranked = analyses.sort((a, b) =>
 *   b.overallSummary.totalScore - a.overallSummary.totalScore
 * );
 *
 * console.log(`Best candidate score: ${ranked[0].overallSummary.totalScore}`);
 * ```
 */
export async function analyzeMultipleResumes(
  resumes: Resume[],
  job: Job,
  model: BaseChatModel,
  options: AnalysisOptions = {}
): Promise<AnalysisResult[]> {
  if (!resumes || resumes.length === 0) {
    throw new Error('At least one resume is required');
  }

  // Process resumes sequentially to avoid overwhelming the AI model
  const results: AnalysisResult[] = [];

  for (const resume of resumes) {
    try {
      const analysis = await analyzeResume(resume, job, model, options);
      results.push(analysis);
    } catch (error) {
      console.warn(
        `Failed to analyze resume, skipping: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      // Continue with other resumes rather than failing completely
    }
  }

  return results;
}

/**
 * Validates input parameters for single resume analysis
 */
function validateInputs(resume: Resume, job: Job, model: BaseChatModel): void {
  if (!resume) {
    throw new Error('Resume is required');
  }

  if (!job) {
    throw new Error('Job is required');
  }

  if (!model) {
    throw new Error('Model is required');
  }
}

/**
 * Calculates overall summary combining all analysis types
 */
function calculateOverallSummary(
  experienceAnalysis: AnalysisResult['experience'],
  skillsAnalysis: AnalysisResult['skills'],
  titleAnalysis: AnalysisResult['title']
): AnalysisResult['overallSummary'] {
  // Calculate component scores
  const experienceScore = calculateExperienceScore(experienceAnalysis);
  const skillsScore = skillsAnalysis.summary.coveragePercentage;
  const titleScore = titleAnalysis.confidence * 100;

  // Calculate weighted total score (experience 40%, skills 40%, title 20%)
  const totalScore = Math.round(
    experienceScore * 0.4 + skillsScore * 0.4 + titleScore * 0.2
  );

  // Determine match quality
  const matchQuality = calculateMatchQuality(
    experienceScore,
    skillsScore,
    titleAnalysis.confidence
  );

  // Determine recommendation
  const isRecommended = shouldRecommendResume(
    matchQuality,
    titleAnalysis.confidence,
    skillsScore
  );

  // Identify strengths and improvements
  const strengths = identifyStrengths(
    experienceAnalysis,
    skillsAnalysis,
    titleAnalysis
  );
  const improvements = identifyImprovements(
    experienceAnalysis,
    skillsAnalysis,
    titleAnalysis
  );

  return {
    totalScore,
    matchQuality,
    strengths,
    improvements,
    isRecommended,
  };
}

/**
 * Calculates a score for experience analysis (0-100)
 */
function calculateExperienceScore(
  experienceAnalysis: AnalysisResult['experience']
): number {
  if (experienceAnalysis.experiences.length === 0) {
    return 0;
  }

  const avgScore = experienceAnalysis.summary.averageRelevanceScore;
  // Convert 1-5 scale to 0-100 scale
  return Math.round(((avgScore - 1) / 4) * 100);
}

/**
 * Identifies key strengths from the analysis
 */
function identifyStrengths(
  experienceAnalysis: AnalysisResult['experience'],
  skillsAnalysis: AnalysisResult['skills'],
  titleAnalysis: AnalysisResult['title']
): string[] {
  const strengths: string[] = [];

  // Experience strengths
  if (experienceAnalysis.summary.averageRelevanceScore >= 4) {
    strengths.push('Highly relevant work experience');
  }
  if (experienceAnalysis.summary.highRelevanceCount >= 2) {
    strengths.push('Multiple high-quality experiences');
  }

  // Skills strengths
  if (skillsAnalysis.summary.coveragePercentage >= 70) {
    strengths.push('Excellent skill coverage');
  }
  if (skillsAnalysis.summary.directMatchCount >= 5) {
    strengths.push('Strong direct skill matches');
  }

  // Title strengths
  if (titleAnalysis.confidence >= 0.8) {
    strengths.push('Well-aligned job title');
  }

  return strengths;
}

/**
 * Identifies areas for improvement from the analysis
 */
function identifyImprovements(
  experienceAnalysis: AnalysisResult['experience'],
  skillsAnalysis: AnalysisResult['skills'],
  titleAnalysis: AnalysisResult['title']
): string[] {
  const improvements: string[] = [];

  // Experience improvements
  if (experienceAnalysis.summary.averageRelevanceScore < 3) {
    improvements.push('Highlight more relevant work experience');
  }

  // Skills improvements
  if (skillsAnalysis.summary.coveragePercentage < 50) {
    improvements.push('Develop missing technical skills');
  }
  if (skillsAnalysis.summary.missingSkillCount > 5) {
    improvements.push('Address key skill gaps');
  }

  // Title improvements
  if (titleAnalysis.suggestedTitle) {
    improvements.push(
      `Consider updating title to: ${titleAnalysis.suggestedTitle}`
    );
  }

  return improvements;
}
