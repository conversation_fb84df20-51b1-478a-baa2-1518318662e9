import { describe, it, expect } from 'vitest';
import type { IsoDate } from '@awe/core';

import { reduceExperienceAnalyses } from './result-reducer';
import type {
  ExperienceAnalysis,
  WorkExperience,
  WorkExperienceScore,
  WorkExperienceImprovement,
} from './resume-types';

describe('Experience Result Reducer', () => {
  const createMockExperience = (
    id: string,
    name: string,
    position: string,
    relevanceScore: number = 3
  ): WorkExperience => ({
    id,
    name,
    position,
    start_date: '2020-01-01' as IsoDate,
    end_date: '2023-12-31' as IsoDate,
    summary: `Summary for ${position} at ${name}`,
    description: `Description for ${position} at ${name}`,
    highlights: [`Achievement 1 at ${name}`, `Achievement 2 at ${name}`],
    location: 'San Francisco, CA',
    url: null,
  });

  const createMockScore = (
    experience: WorkExperience,
    relevanceScore: number = 3
  ): WorkExperienceScore => ({
    experience,
    relevanceScore,
    reasoning: `This experience is relevant because of ${experience.position}`,
  });

  const createMockImprovement = (
    experienceId: string,
    field: 'summary' | 'description' | 'highlights' = 'summary'
  ): WorkExperience => ({
    experienceId,
    field,
    originalContent: `Original ${field} content`,
    improvedContent: `Improved ${field} content`,
    reasoning: `Improvement reasoning for ${field}`,
    diffPreview: `- Original ${field} content\n+ Improved ${field} content`,
  });

  const createMockAnalysis = (
    resumeIndex: number,
    experiences: SingleResumeWorkExperience[]
  ): SingleResumeExperienceAnalysis => {
    const scoredExperiences = experiences.map((exp) =>
      createMockScore(exp, 3 + resumeIndex)
    );
    const improvements = experiences.slice(0, 1).map((exp) =>
      createMockImprovement(exp.id)
    );

    return {
      experiences,
      scoredExperiences,
      improvements,
      summary: {
        totalExperiences: experiences.length,
        averageRelevanceScore: 3 + resumeIndex,
        highRelevanceCount: experiences.filter((_, i) => i === 0).length,
        improvementsProposed: improvements.length,
      },
    };
  };

  describe('reduceExperienceAnalyses', () => {
    it('should combine experiences from multiple single-resume analyses', () => {
      const exp1 = createMockExperience('exp1', 'Company A', 'Developer');
      const exp2 = createMockExperience('exp2', 'Company B', 'Engineer');
      const exp3 = createMockExperience('exp3', 'Company C', 'Architect');

      const analysis1 = createMockAnalysis(0, [exp1, exp2]);
      const analysis2 = createMockAnalysis(1, [exp3]);

      const result = reduceExperienceAnalyses([analysis1, analysis2]);

      expect(result.experiences).toHaveLength(3);
      expect(result.experiences.map((e) => e.id)).toEqual(['exp1', 'exp2', 'exp3']);
      expect(result.experiences[0].sourceResumes).toEqual([0]);
      expect(result.experiences[1].sourceResumes).toEqual([0]);
      expect(result.experiences[2].sourceResumes).toEqual([1]);
    });

    it('should consolidate similar experiences from different resumes', () => {
      // Same company and position but from different resumes
      const exp1 = createMockExperience('exp1', 'Acme Corp', 'Software Engineer');
      const exp2 = createMockExperience('exp2', 'Acme Corp', 'Software Engineer');

      const analysis1 = createMockAnalysis(0, [exp1]);
      const analysis2 = createMockAnalysis(1, [exp2]);

      const result = reduceExperienceAnalyses([analysis1, analysis2]);

      // Should consolidate into one experience
      expect(result.experiences).toHaveLength(1);
      expect(result.experiences[0].sourceResumes).toEqual([0, 1]);
      expect(result.experiences[0].name).toBe('Acme Corp');
      expect(result.experiences[0].position).toBe('Software Engineer');
    });

    it('should keep highest relevance scores when consolidating', () => {
      const exp1 = createMockExperience('exp1', 'Company A', 'Developer');
      const exp2 = createMockExperience('exp2', 'Company A', 'Developer');

      const analysis1 = createMockAnalysis(0, [exp1]);
      const analysis2 = createMockAnalysis(1, [exp2]);

      // Manually set different scores
      analysis1.scoredExperiences[0].relevanceScore = 3;
      analysis2.scoredExperiences[0].relevanceScore = 5;

      const result = reduceExperienceAnalyses([analysis1, analysis2]);

      expect(result.scoredExperiences).toHaveLength(1);
      expect(result.scoredExperiences[0].relevanceScore).toBe(5); // Higher score kept
    });

    it('should merge improvements from all analyses', () => {
      const exp1 = createMockExperience('exp1', 'Company A', 'Developer');
      const exp2 = createMockExperience('exp2', 'Company B', 'Engineer');

      const analysis1 = createMockAnalysis(0, [exp1]);
      const analysis2 = createMockAnalysis(1, [exp2]);

      const result = reduceExperienceAnalyses([analysis1, analysis2]);

      expect(result.improvements).toHaveLength(2);
      expect(result.improvements.map((i) => i.experienceId)).toContain('exp1');
      expect(result.improvements.map((i) => i.experienceId)).toContain('exp2');
    });

    it('should calculate correct summary statistics', () => {
      const exp1 = createMockExperience('exp1', 'Company A', 'Developer');
      const exp2 = createMockExperience('exp2', 'Company B', 'Engineer');
      const exp3 = createMockExperience('exp3', 'Company C', 'Architect');

      const analysis1 = createMockAnalysis(0, [exp1, exp2]);
      const analysis2 = createMockAnalysis(1, [exp3]);

      // Set specific scores
      analysis1.scoredExperiences[0].relevanceScore = 4;
      analysis1.scoredExperiences[1].relevanceScore = 2;
      analysis2.scoredExperiences[0].relevanceScore = 5;

      const result = reduceExperienceAnalyses([analysis1, analysis2]);

      expect(result.summary.totalExperiences).toBe(3);
      expect(result.summary.averageRelevanceScore).toBeCloseTo(3.67, 2);
      expect(result.summary.highRelevanceCount).toBe(2); // Scores >= 4
      expect(result.summary.improvementsProposed).toBe(2);
    });

    it('should handle empty analyses array', () => {
      const result = reduceExperienceAnalyses([]);

      expect(result.experiences).toHaveLength(0);
      expect(result.scoredExperiences).toHaveLength(0);
      expect(result.improvements).toHaveLength(0);
      expect(result.summary.totalExperiences).toBe(0);
      expect(result.summary.averageRelevanceScore).toBe(0);
      expect(result.summary.highRelevanceCount).toBe(0);
      expect(result.summary.improvementsProposed).toBe(0);
    });

    it('should handle analyses with no experiences', () => {
      const emptyAnalysis: SingleResumeExperienceAnalysis = {
        experiences: [],
        scoredExperiences: [],
        improvements: [],
        summary: {
          totalExperiences: 0,
          averageRelevanceScore: 0,
          highRelevanceCount: 0,
          improvementsProposed: 0,
        },
      };

      const result = reduceExperienceAnalyses([emptyAnalysis]);

      expect(result.experiences).toHaveLength(0);
      expect(result.scoredExperiences).toHaveLength(0);
      expect(result.improvements).toHaveLength(0);
      expect(result.summary.totalExperiences).toBe(0);
    });

    it('should preserve experience order by relevance score', () => {
      const exp1 = createMockExperience('exp1', 'Company A', 'Junior Dev');
      const exp2 = createMockExperience('exp2', 'Company B', 'Senior Dev');
      const exp3 = createMockExperience('exp3', 'Company C', 'Lead Dev');

      const analysis1 = createMockAnalysis(0, [exp1, exp2, exp3]);

      // Set scores in non-descending order
      analysis1.scoredExperiences[0].relevanceScore = 2; // Junior
      analysis1.scoredExperiences[1].relevanceScore = 5; // Senior
      analysis1.scoredExperiences[2].relevanceScore = 4; // Lead

      const result = reduceExperienceAnalyses([analysis1]);

      // Should be ordered by relevance score (descending)
      expect(result.scoredExperiences[0].relevanceScore).toBe(5); // Senior first
      expect(result.scoredExperiences[1].relevanceScore).toBe(4); // Lead second
      expect(result.scoredExperiences[2].relevanceScore).toBe(2); // Junior last
    });
  });
});
