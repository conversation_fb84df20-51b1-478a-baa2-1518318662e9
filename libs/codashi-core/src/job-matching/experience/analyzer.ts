import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { ExperienceExtractor } from './extractor';
import { ExperienceScorer } from './scorer';
import type {
  ExperienceAnalysis,
  ExperienceAnalysisOptions,
} from './resume-types';

/**
 * Analyzes work experience matches between a single resume and a job posting.
 *
 * This function processes exactly one resume against one job description,
 * eliminating all complex consolidation logic. It extracts experiences directly
 * from the single resume and scores them against job requirements. Content
 * improvements are handled at the orchestrator level.
 *
 * @param resume - Single resume to analyze
 * @param job - Job posting to match against
 * @param model - LangChain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive single resume experience analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeExperience(
 *   resume,
 *   jobPosting,
 *   mistralModel,
 *   {
 *     batchSize: 5
 *   }
 * );
 *
 * console.log(`Total experiences: ${analysis.summary.totalExperiences}`);
 * console.log(`Average relevance: ${analysis.summary.averageRelevanceScore}`);
 * ```
 */
export async function analyzeExperience(
  resume: Resume,
  job: Job,
  model: BaseChatModel,
  options: ExperienceAnalysisOptions = {}
): Promise<ExperienceAnalysis> {
  if (!resume) {
      return {
        experiences: [],
        scoredExperiences: [],
        improvements: [],
        summary: {
          totalExperiences: 0,
          averageRelevanceScore: 0,
          highRelevanceCount: 0,
          improvementsProposed: 0,
        },
      };
    }

  try {
    // Step 1: Extract work experiences from the single resume (no consolidation)
    const extractor = new ExperienceExtractor();
    const experiences = extractor.extractExperiences(resume);

    // Handle case where no work experiences are found
    if (experiences.length === 0) {
      return {
        experiences: [],
        scoredExperiences: [],
        improvements: [],
        summary: {
          totalExperiences: 0,
          averageRelevanceScore: 0,
          highRelevanceCount: 0,
          improvementsProposed: 0,
        },
      };
    }

    // Step 2: Score experiences against job requirements
    const scorer = new ExperienceScorer(model);
    const scoredExperiences = await withTimeout(
      scorer.scoreExperiences(experiences, job, finalOptions),
      finalOptions.timeoutMs,
      'Single resume experience scoring'
    );

    // Step 3: Skip content improvements - will be handled at orchestrator level
    const improvements: ExperienceAnalysis['improvements'] = [];

    // Step 4: Calculate summary statistics
    const summary = calculateSummary(scoredExperiences, improvements);

    return {
      experiences,
      scoredExperiences,
      improvements,
      summary,
    };
  } catch (error) {
    if (isAIUnavailableError(error)) {
      throw new ExperienceMatchError(
        'AI model is currently unavailable for experience analysis',
        'AI_UNAVAILABLE'
      );
    }

    if (isTimeoutError(error)) {
      throw new ExperienceMatchError(
        'Experience analysis timed out',
        'TIMEOUT'
      );
    }

    throw new ExperienceMatchError(
      `Experience analysis failed: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      'INVALID_INPUT'
    );
  }
}

/**
 * Calculates summary statistics for the single resume experience analysis
 */
function calculateSummary(
  scoredExperiences: ExperienceAnalysis['scoredExperiences'],
  improvements: ExperienceAnalysis['improvements']
): ExperienceAnalysis['summary'] {
  const totalExperiences = scoredExperiences.length;

  if (totalExperiences === 0) {
    return {
      totalExperiences: 0,
      averageRelevanceScore: 0,
      highRelevanceCount: 0,
      improvementsProposed: improvements.length,
    };
  }

  const totalScore = scoredExperiences.reduce(
    (sum, scored) => sum + scored.relevanceScore,
    0
  );
  const averageRelevanceScore =
    Math.round((totalScore / totalExperiences) * 10) / 10;

  const highRelevanceCount = scoredExperiences.filter(
    (scored) => scored.relevanceScore >= 4
  ).length;

  return {
    totalExperiences,
    averageRelevanceScore,
    highRelevanceCount,
    improvementsProposed: improvements.length,
  };
}

/**
 * Error class for single resume experience matching operations
 */
export class ExperienceMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_EXPERIENCES'
  ) {
    super(message);
    this.name = 'ExperienceMatchError';
  }
}
