import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { withTimeout } from '../../utils/common-utils';
import {
  getConfidenceLevel,
  TitleAnalysis,
  TitleAnalysisOptions,
  titleAnalysisSchema,
  TitleMatchError,
} from '../types';

/**
 * Analyzes title match between a single resume and a job posting.
 *
 * This function processes exactly one resume against one job description,
 * analyzing how well the resume's title matches the job title and providing
 * suggestions for improvement if needed.
 *
 * @param resume - Single resume to analyze
 * @param job - Job posting to match against
 * @param model - Lang<PERSON>hain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive single-resume title analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeTitle(
 *   resume,
 *   jobPosting,
 *   mistralModel,
 *   { suggestImprovements: true, confidenceThreshold: 0.6 }
 * );
 *
 * console.log(`Confidence: ${analysis.confidence}`);
 * console.log(`Match quality: ${analysis.summary.confidenceLevel}`);
 * if (analysis.suggestedTitle) {
 *   console.log(`Suggested improvement: ${analysis.suggestedTitle}`);
 * }
 * ```
 */
export async function analyzeTitle(
  resume: Resume,
  job: Job,
  model: BaseChatModel,
  options: TitleAnalysisOptions = {}
): Promise<TitleAnalysis> {
  try {
    validateInputs(resume, job, model);

    const finalOptions = {
      timeoutMs: 15000, // 15 seconds default timeout
      suggestImprovements: true,
      confidenceThreshold: 0.0,
      ...options,
    } satisfies TitleAnalysisOptions;

    // Extract the title from the resume
    const originalTitle = extractResumeTitle(resume);

    if (!originalTitle) {
      throw new TitleMatchError(
        'No title found in resume. Resume must have a title in the header section.'
      );
    }

    // Analyze the title match using AI
    const aiResult = await analyzeWithAI(
      originalTitle,
      job.title,
      model,
      finalOptions
    );

    // Build the result
    const analysis: TitleAnalysis = {
      originalTitle,
      jobTitle: job.title,
      confidence: aiResult.confidence,
      reasoning: aiResult.reasoning,
      suggestedTitle: aiResult.suggestedTitle,
      summary: {
        isGoodMatch: aiResult.isGoodMatch,
        confidenceLevel: getConfidenceLevel(aiResult.confidence),
        hasImprovement: !!aiResult.suggestedTitle,
      },
    };

    return analysis;
  } catch (error) {
    throw new TitleMatchError(
      `Failed to analyze single resume title: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      error
    );
  }
}

/**
 * Validates input parameters
 */
function validateInputs(resume: Resume, job: Job, model: BaseChatModel): void {
  if (!resume) {
    throw new TitleMatchError('Resume is required');
  }

  if (!job) {
    throw new TitleMatchError('Job is required');
  }

  if (!model) {
    throw new TitleMatchError('Model is required');
  }

  if (!job.title || job.title.trim() === '') {
    throw new TitleMatchError('Job must have a title');
  }
}

/**
 * Extracts the title from a resume's header section
 */
function extractResumeTitle(resume: Resume): string | null {
  // Check if resume has header structure
  if (!resume.header?.items) {
    return null;
  }

  // Find the title item in the header
  const titleItem = resume.header.items.find((item) => item.name === 'title');

  if (!titleItem?.value || titleItem.value.trim() === '') {
    return null;
  }

  return titleItem.value.trim();
}

/**
 * Analyzes title match using AI
 */
async function analyzeWithAI(
  resumeTitle: string,
  jobTitle: string,
  model: BaseChatModel,
  options: TitleAnalysisOptions
) {
  const prompt = ChatPromptTemplate.fromTemplate(`
You are an expert career advisor analyzing how well a resume title matches a job posting.

Resume Title: "{resumeTitle}"
Job Title: "{jobTitle}"

Analyze the match between these titles and provide:

1. **Confidence Score (0-1)**: How well does the resume title match the job title?
   - 0.9-1.0: Excellent match (same role, similar seniority)
   - 0.7-0.8: Good match (same role, minor differences)
   - 0.5-0.6: Fair match (related role, some transferable skills)
   - 0.3-0.4: Poor match (different role, limited transferability)
   - 0.0-0.2: Very poor match (unrelated roles)

2. **Reasoning**: Explain your assessment, considering:
   - Role similarity (e.g., "Developer" vs "Engineer")
   - Seniority level (e.g., "Junior", "Senior", "Lead")
   - Industry/domain alignment
   - Technology stack implications

3. **Good Match Assessment**: Is this a good match overall?

4. **Title Improvement** (if requested and confidence < 0.8):
   - Suggest a better title that maintains the candidate's experience level
   - Keep improvements minimal and realistic
   - Don't inflate seniority beyond what's justified

Guidelines:
- Be realistic about seniority levels
- Consider industry variations in title conventions
- Focus on role function over exact wording
- Account for career progression and transferable skills

{format_instructions}
  `);

  const parser = StructuredOutputParser.fromZodSchema(titleAnalysisSchema);
  const chain = RunnableSequence.from([prompt, model, parser]);

  const operation = chain.invoke({
    resumeTitle,
    jobTitle,
    format_instructions: parser.getFormatInstructions(),
  });

  const result = options.timeoutMs
    ? await withTimeout(operation, options.timeoutMs, 'Title analysis')
    : await operation;

  // Filter out suggested title if improvements are not requested
  if (!options.suggestImprovements) {
    result.suggestedTitle = undefined;
  }

  // Filter out suggested title if confidence is above threshold
  if (result.confidence >= (options.confidenceThreshold || 0.8)) {
    result.suggestedTitle = undefined;
  }

  return result;
}

/**
 * Helper function to get title similarity score
 */
export function getTitleSimilarityScore(
  resumeTitle: string,
  jobTitle: string
): number {
  const normalizeTitle = (title: string) =>
    title
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter((word) => word.length > 2);

  const resumeWords = normalizeTitle(resumeTitle);
  const jobWords = normalizeTitle(jobTitle);

  if (resumeWords.length === 0 || jobWords.length === 0) {
    return 0;
  }

  // Calculate word overlap
  const commonWords = resumeWords.filter((word) =>
    jobWords.some((jobWord) => jobWord.includes(word) || word.includes(jobWord))
  );

  // Simple similarity based on word overlap
  const similarity =
    commonWords.length / Math.max(resumeWords.length, jobWords.length);

  return Math.min(similarity, 1.0);
}

/**
 * Helper function to extract seniority level from title
 */
export function extractSeniorityLevel(
  title: string
): 'junior' | 'mid' | 'senior' | 'lead' | 'unknown' {
  const lowerTitle = title.toLowerCase();

  if (lowerTitle.includes('junior') || lowerTitle.includes('jr')) {
    return 'junior';
  }

  if (lowerTitle.includes('senior') || lowerTitle.includes('sr')) {
    return 'senior';
  }

  if (
    lowerTitle.includes('lead') ||
    lowerTitle.includes('principal') ||
    lowerTitle.includes('staff')
  ) {
    return 'lead';
  }

  // If no explicit seniority markers, assume mid-level
  if (!lowerTitle.includes('intern') && !lowerTitle.includes('entry')) {
    return 'mid';
  }

  return 'unknown';
}
